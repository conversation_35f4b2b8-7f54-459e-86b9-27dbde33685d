import { Card, CardContent } from "@/components/ui/card";
import { Calendar, Zap, BookO<PERSON>, Sparkles, Quote } from "lucide-react";
import { toast } from "sonner";
import { useEffect, useState } from "react";
import { detailedWordData } from "@/data/dictionary";
import { motion } from "framer-motion";

const WordOfDay = () => {
  const [wordOfDay, setWordOfDay] = useState<{
    word: string;
    pronunciation: string;
    partOfSpeech: string;
    definition: string;
    example: string;
    etymology: string;
  } | null>(null);

  useEffect(() => {
    // Get a word based on the current date
    const getWordOfDay = () => {
      // Get all available words
      const allWords = Object.entries(detailedWordData);
      if (allWords.length === 0) return null;
      
      // Use the current date to deterministically select a word
      const now = new Date();
      const dateStr = `${now.getFullYear()}-${now.getMonth()}-${now.getDate()}`;
      
      // Create a simple hash of the date string
      let hash = 0;
      for (let i = 0; i < dateStr.length; i++) {
        hash = ((hash << 5) - hash) + dateStr.charCodeAt(i);
        hash = hash & hash; // Convert to 32bit integer
      }
      
      // Use the hash to select a word
      const index = Math.abs(hash) % allWords.length;
      const [word, details] = allWords[index];
      
      // Format the pronunciation
      const pronunciation = `/${word.split('').join('-')}/`;
      
      return {
        word,
        pronunciation,
        partOfSpeech: details.partOfSpeech,
        definition: details.definition,
        example: details.example,
        etymology: details.etymology
      };
    };
    
    const selectedWord = getWordOfDay();
    if (selectedWord) {
      setWordOfDay(selectedWord);
    }
  }, []);
  
  // Pronunciation functionality removed as requested

  const today = new Date().toLocaleDateString('tl-PH', { 
    month: 'long', 
    day: 'numeric',
    year: 'numeric'
  });

  return (
    <motion.div
      initial={{ opacity: 0, y: 30, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
    >
      <Card className="relative border-0 shadow-2xl overflow-hidden rounded-2xl bg-gradient-to-br from-white via-gold-1/5 to-maroon/5 min-h-[400px] hover:shadow-3xl transition-all duration-500">
        {/* Decorative Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-gold-1/10 to-maroon/10"></div>
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-gold-1/20 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-maroon/20 to-transparent rounded-full blur-2xl"></div>

        {/* Header Section */}
        <div className="relative bg-gradient-to-r from-maroon via-burgundy to-maroon text-white py-6 px-8">
          <div className="flex items-center justify-between">
            <motion.div
              className="flex items-center"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="relative">
                <Sparkles className="h-7 w-7 mr-4 text-gold-1 animate-pulse" />
                <div className="absolute inset-0 h-7 w-7 mr-4 text-gold-1/30 animate-ping"></div>
              </div>
              <div>
                <h2 className="text-2xl md:text-3xl font-serif font-bold tracking-wide">Salita ng Araw</h2>
                <p className="text-gold-1/80 text-sm font-medium mt-1">Palawakin ang inyong bokabularyo</p>
              </div>
            </motion.div>
            <motion.div
              className="flex items-center bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <Calendar className="h-4 w-4 mr-2 text-gold-1" />
              <span className="text-sm font-medium">{today}</span>
            </motion.div>
          </div>
        </div>

        {/* Content Section */}
        <CardContent className="relative p-8 md:p-10">
          {wordOfDay ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              {/* Word Title Section */}
              <div className="mb-8 text-center">
                <motion.h3
                  className="text-4xl md:text-5xl lg:text-6xl font-serif font-bold text-transparent bg-gradient-to-r from-maroon via-burgundy to-maroon bg-clip-text mb-3"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.8, delay: 0.5 }}
                >
                  {wordOfDay.word}
                </motion.h3>
                <motion.div
                  className="inline-flex items-center bg-gold-1/10 border border-gold-1/30 rounded-full px-4 py-2"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.6 }}
                >
                  <BookOpen className="h-4 w-4 mr-2 text-maroon" />
                  <span className="text-maroon font-medium text-sm">{wordOfDay.pronunciation}</span>
                </motion.div>
              </div>

              {/* Information Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                {/* Definition Card */}
                <motion.div
                  className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-maroon/10 shadow-sm hover:shadow-md transition-all duration-300"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.7 }}
                >
                  <div className="flex items-center mb-3">
                    <div className="w-8 h-8 bg-maroon/10 rounded-full flex items-center justify-center mr-3">
                      <BookOpen className="h-4 w-4 text-maroon" />
                    </div>
                    <h4 className="font-bold text-maroon text-sm tracking-wider uppercase">Kahulugan</h4>
                  </div>
                  <p className="text-dictionary-dark leading-relaxed text-base">{wordOfDay.definition}</p>
                </motion.div>

                {/* Part of Speech Card */}
                {wordOfDay.partOfSpeech && (
                  <motion.div
                    className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-maroon/10 shadow-sm hover:shadow-md transition-all duration-300"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: 0.8 }}
                  >
                    <div className="flex items-center mb-3">
                      <div className="w-8 h-8 bg-gold-1/20 rounded-full flex items-center justify-center mr-3">
                        <Zap className="h-4 w-4 text-gold-2" />
                      </div>
                      <h4 className="font-bold text-maroon text-sm tracking-wider uppercase">Uri ng Salita</h4>
                    </div>
                    <p className="text-dictionary-dark leading-relaxed text-base font-medium">{wordOfDay.partOfSpeech}</p>
                  </motion.div>
                )}
              </div>

              {/* Example Section */}
              {wordOfDay.example && (
                <motion.div
                  className="bg-gradient-to-r from-gold-1/5 to-maroon/5 rounded-xl p-6 border border-gold-1/20 mb-6"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.9 }}
                >
                  <div className="flex items-start">
                    <Quote className="h-6 w-6 text-maroon/60 mr-4 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-bold text-maroon text-sm tracking-wider uppercase mb-2">Halimbawa</h4>
                      <p className="text-dictionary-dark leading-relaxed text-base italic">
                        "{wordOfDay.example}"
                      </p>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Etymology Section */}
              {wordOfDay.etymology && (
                <motion.div
                  className="mt-8 pt-6 border-t border-gradient-to-r from-transparent via-gold-1/30 to-transparent"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.0 }}
                >
                  <div className="bg-white/40 backdrop-blur-sm rounded-xl p-6 border border-maroon/10">
                    <div className="flex items-center mb-3">
                      <div className="w-8 h-8 bg-maroon/10 rounded-full flex items-center justify-center mr-3">
                        <Sparkles className="h-4 w-4 text-maroon" />
                      </div>
                      <h4 className="font-bold text-maroon text-sm tracking-wider uppercase">Etimolohiya</h4>
                    </div>
                    <p className="text-muted-foreground leading-relaxed text-sm">
                      {wordOfDay.etymology}
                    </p>
                  </div>
                </motion.div>
              )}
            </motion.div>
          ) : (
            <motion.div
              className="text-center py-16"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6 }}
            >
              <div className="inline-flex items-center justify-center w-16 h-16 bg-maroon/10 rounded-full mb-4">
                <div className="w-6 h-6 border-2 border-maroon/30 border-t-maroon rounded-full animate-spin"></div>
              </div>
              <p className="text-lg text-muted-foreground">Naglo-load ang salita ng araw...</p>
            </motion.div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default WordOfDay;
