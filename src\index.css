/* Using Helvetica as the primary font */

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 50% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 0 100% 25%;
    --primary-foreground: 210 40% 98%;

    --secondary: 0 59% 13%;
    --secondary-foreground: 210 40% 98%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 43 100% 50%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 0 100% 25%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 0 100% 25%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 0 59% 13%;
  }

  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-serif font-semibold;
  }

  .definition-text p {
    @apply mb-3 leading-relaxed;
  }
  
  .definition-text .part-of-speech {
    @apply text-maroon italic mb-1;
  }

  .definition-text .definition {
    @apply mb-2;
  }

  .definition-text .example {
    @apply text-muted-foreground italic pl-4 border-l-2 border-muted mb-3;
  }

  .word-card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
  }
  
  .dictionary-gradient {
    @apply bg-gradient-to-r from-burgundy to-maroon;
  }

  /* Enhanced Gold Accents */
  .gold-border {
    @apply border-2 border-gold-1 shadow-lg;
  }
  
  .gold-accent {
    @apply text-gold-2 font-bold;
  }
  
  .gold-highlight {
    @apply bg-gold-1/10 border border-gold-1/30;
  }
  
  .gold-button {
    @apply bg-gold-1 hover:bg-gold-2 text-burgundy font-bold transition-all duration-300;
  }
  
  .maroon-gold-gradient {
    @apply bg-gradient-to-r from-maroon to-gold-2;
  }
  
  .outline-gold {
    @apply outline outline-2 outline-gold-1;
  }
  
  /* Enhanced Animations */
  .hover-lift {
    @apply transition-transform duration-300 hover:-translate-y-1;
  }

  .hover-scale {
    @apply transition-transform duration-200 hover:scale-105;
  }

  .fade-in {
    @apply animate-fade-in;
  }

  .pulse-gold {
    @apply animate-pulse border-gold-1;
  }

  .float {
    @apply animate-float;
  }

  /* Enhanced Shadow Effects */
  .shadow-3xl {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
  }

  .shadow-glow {
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
  }

  .shadow-maroon {
    box-shadow: 0 10px 25px rgba(128, 0, 0, 0.15);
  }

  /* Gradient Text Effects */
  .text-gradient-maroon {
    @apply text-transparent bg-gradient-to-r from-maroon via-burgundy to-maroon bg-clip-text;
  }

  .text-gradient-gold {
    @apply text-transparent bg-gradient-to-r from-gold-1 to-gold-2 bg-clip-text;
  }
  
  /* Enhanced Navigation Styles with Smooth Transitions */
  .nav-link {
    @apply relative after:absolute after:bottom-0 after:left-0 after:h-[2px] after:w-full after:origin-bottom-right after:scale-x-0 after:bg-maroon after:transition-transform after:duration-300 hover:after:origin-bottom-left hover:after:scale-x-100;
  }
  
  .nav-link.active {
    @apply after:scale-x-100 after:origin-bottom-left;
  }
  
  /* Page Transitions */
  .page-transition {
    @apply transition-all duration-500 ease-in-out;
  }
  
  /* Page Enter Animation */
  .page-enter {
    @apply opacity-0 translate-y-4;
  }
  
  .page-enter-active {
    @apply opacity-100 translate-y-0 transition-all duration-500;
  }
  
  /* Page Exit Animation */
  .page-exit {
    @apply opacity-100 translate-y-0;
  }
  
  .page-exit-active {
    @apply opacity-0 translate-y-4 transition-all duration-500;
  }
  
  /* Form Focus Styles */
  input:focus, textarea:focus, select:focus {
    @apply border-gold-1/50 outline-gold-1/50 transition-all duration-200;
  }
  
  /* Dictionary Entry Animation */
  .dictionary-entry {
    @apply opacity-0;
    animation: slide-up 0.5s ease forwards;
  }
  
  @keyframes slide-up {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
  
  /* Staggered Animation for List Items */
  .stagger-item:nth-child(1) { animation-delay: 0.1s; }
  .stagger-item:nth-child(2) { animation-delay: 0.2s; }
  .stagger-item:nth-child(3) { animation-delay: 0.3s; }
  .stagger-item:nth-child(4) { animation-delay: 0.4s; }
  .stagger-item:nth-child(5) { animation-delay: 0.5s; }
  .stagger-item:nth-child(6) { animation-delay: 0.6s; }
  
  /* Improved Card Design */
  .gold-card {
    @apply bg-white rounded-xl shadow-sm border border-gold-1/20 hover:border-gold-1/50 transition-all duration-300;
  }

  /* Word Detail Styles */
  .word-detail h4 {
    @apply text-maroon;
  }

  /* Tab Switch Animation */
  .tab-content {
    @apply transition-all duration-500 ease-in-out;
  }

  .tab-enter {
    @apply opacity-0 translate-y-4;
  }

  .tab-enter-active {
    @apply opacity-100 translate-y-0;
  }

  .tab-exit {
    @apply opacity-100;
  }

  .tab-exit-active {
    @apply opacity-0 translate-y-4;
  }
  
  /* Custom Scrollbar Styles */
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-gold-1/10 rounded-full;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-maroon/60 rounded-full transition-colors duration-300;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-maroon/80;
  }
  
  .custom-scrollbar::-webkit-scrollbar-corner {
    @apply bg-transparent;
  }
  
  /* Firefox scrollbar */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: theme('colors.maroon / 0.6') theme('colors.gold.1 / 0.1');
  }
}

@layer base {
  * {
    @apply border-border;
  }
}
